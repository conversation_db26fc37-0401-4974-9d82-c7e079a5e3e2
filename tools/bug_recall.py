import pandas as pd
import json
import re
import os
import sys
from datetime import datetime
from utils.logger.logger import Logging
from utils.llm.chat import chat_stream
from utils.llm.prompt import extract_bug_modules_prompt, analyze_bug_relevance_prompt, generate_bug_test_cases_prompt
from utils.tapd import TAPDUtils
from rag_server.trag_manager import TRAGManager
from rag_server.search import search_document

# 添加bug配置路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from bug.config.bug_recall_config import get_config

logger = Logging().get_logger()


class BugRecallTool:
    """Bug召回工具类 - 基于TRAG向量搜索"""

    def __init__(self, bug_namespace=None, bug_collection=None):
        self.tapd_utils = TAPDUtils()
        # 初始化Bug知识库连接
        self.bug_namespace = bug_namespace or os.getenv("BUG_NAMESPACE")
        self.bug_collection = bug_collection or os.getenv("BUG_COLLECTION")

        if self.bug_namespace and self.bug_collection:
            try:
                self.trag_manager = TRAGManager(ns_code=self.bug_namespace, coll_code=self.bug_collection)
                logger.info(f"已连接Bug知识库: {self.bug_namespace}/{self.bug_collection}")
            except Exception as e:
                logger.warning(f"无法连接Bug知识库: {str(e)}")
                self.trag_manager = None
        else:
            logger.warning("未配置Bug知识库信息，将使用默认搜索方式")
            self.trag_manager = None
    
    def gen_bug_file_path(self, story_name):
        """生成bug召回结果文件路径"""
        # 从配置获取输出目录和格式
        base_dir = get_config('output.base_dir') or "data/bug_recall"
        date_format = get_config('output.date_format') or "%Y%m%d"
        timestamp_format = get_config('output.timestamp_format') or "%Y%m%d_%H%M%S"
        file_format = get_config('output.file_format') or "xlsx"

        # 生成日期子目录
        date_subdir = datetime.now().strftime(date_format)
        case_dir = os.path.join(base_dir, date_subdir)

        # 确保输出目录存在
        os.makedirs(case_dir, exist_ok=True)

        # 替换文件名中的特殊字符
        safe_name = re.sub(r'[\\/:*?"<>|]', '_', story_name)

        # 生成唯一的文件名
        timestamp = datetime.now().strftime(timestamp_format)
        file_path = os.path.join(case_dir, f"{safe_name}_bug_recall_{timestamp}.{file_format}")

        # 确保父目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        return file_path
    
    def search_bugs_from_trag(self, story):
        """
        从TRAG向量数据库中搜索相关Bug
        :param story: 需求文档
        :return: 相关Bug列表
        """
        try:
            if not self.trag_manager:
                logger.warning("Bug知识库未配置，无法进行向量搜索")
                return []

            # 构建搜索查询
            search_query = self._build_search_query(story)
            logger.info(f"搜索查询: {search_query[:200]}...")

            # 从配置获取搜索参数
            max_bugs = get_config('analysis.max_bugs_per_story') or 50

            # 使用TRAG搜索相关Bug
            search_results = self.trag_manager.search_test_cases(
                query=search_query,
                limit=max_bugs
            )

            logger.info(f"从TRAG搜索到 {len(search_results)} 个相关Bug")

            # 处理搜索结果
            bugs = []
            for result in search_results:
                try:
                    # 解析Bug数据
                    bug_data = json.loads(result.doc) if isinstance(result.doc, str) else result.doc
                    bug_data['trag_score'] = result.score  # 添加TRAG相似度评分
                    bugs.append(bug_data)
                except Exception as e:
                    logger.warning(f"解析Bug数据失败: {str(e)}")
                    continue

            return bugs

        except Exception as e:
            logger.error(f"从TRAG搜索Bug时发生错误：{str(e)}")
            return []

    def _build_search_query(self, story):
        """
        构建用于TRAG搜索的查询字符串
        :param story: 需求文档
        :return: 搜索查询字符串
        """
        # 提取关键信息构建搜索查询
        query_parts = []

        if story.get('name'):
            query_parts.append(f"需求名称: {story['name']}")

        if story.get('description'):
            # 截取描述的前500个字符避免查询过长
            description = story['description'][:500]
            query_parts.append(f"需求描述: {description}")

        if story.get('category_name'):
            query_parts.append(f"分类: {story['category_name']}")

        return " ".join(query_parts)


    def analyze_bugs_relevance(self, story, bugs):
        """
        分析bug与需求的相关性，直接取前10个BUG交给大模型分析
        :param story: 需求文档
        :param bugs: bug列表
        :return: 相关bug列表
        """
        try:
            if not bugs:
                return []

            # 从配置获取参数
            max_relevant_bugs = get_config('analysis.max_relevant_bugs') or 20
            max_tokens = get_config('llm.max_tokens') or 8096

            # 直接取前10个BUG，不进行TRAG评分筛选
            sorted_bugs = sorted(bugs, key=lambda x: x.get('trag_score', 0), reverse=True)
            top_10_bugs = sorted_bugs[:10]
            logger.info(f"直接取前10个Bug进行LLM分析，共 {len(top_10_bugs)} 个Bug")

            # 为每个Bug添加TRAG相关性评分（用于后续显示）
            for bug in top_10_bugs:
                trag_score = bug.get('trag_score', 0)
                # TRAG评分通常在0-1之间，转换为0-10分制
                normalized_score = trag_score * 10 if trag_score <= 1 else trag_score
                bug['trag_relevance_score'] = normalized_score

            # 使用LLM进行深度相关性分析
            bugs_json = json.dumps(top_10_bugs, ensure_ascii=False, indent=2)
            prompt = analyze_bug_relevance_prompt(story['description'], bugs_json)
            relevant_bugs = chat_stream(prompt, max_tokens=max_tokens).choices[0].message.content
            logger.info(f"LLM分析的相关bug: {relevant_bugs[:200]}...")

            # 清理响应内容（去除可能的代码标记）
            cleaned_content = re.sub(r'^```(json)?\s*|\s*```$', '', relevant_bugs, flags=re.MULTILINE).strip()

            try:
                relevant_bugs = json.loads(cleaned_content)
            except (ValueError, SyntaxError) as e:
                logger.error(f"解析相关bug失败：{str(e)}")
                # 如果LLM解析失败，返回前10个Bug
                return top_10_bugs[:max_relevant_bugs]

            # 限制返回数量
            if len(relevant_bugs) > max_relevant_bugs:
                relevant_bugs = relevant_bugs[:max_relevant_bugs]

            logger.info(f"最终筛选出 {len(relevant_bugs)} 个相关Bug")
            return relevant_bugs

        except Exception as e:
            logger.error(f"分析bug相关性时发生错误：{str(e)}")
            return []
    
    def generate_bug_test_cases(self, story, relevant_bugs):
        """
        基于相关bug生成测试用例建议
        :param story: 需求文档
        :param relevant_bugs: 相关bug列表
        :return: 测试用例列表
        """
        try:
            if not relevant_bugs:
                return []

            # 从配置获取参数
            max_test_cases = get_config('analysis.max_test_cases') or 15
            max_tokens = get_config('llm.max_tokens') or 8096

            bugs_json = json.dumps(relevant_bugs, ensure_ascii=False, indent=2)
            prompt = generate_bug_test_cases_prompt(story['description'], bugs_json)
            test_cases = chat_stream(prompt, max_tokens=max_tokens).choices[0].message.content
            logger.info(f"生成的bug测试用例: {test_cases[:200]}...")

            # 清理响应内容（去除可能的代码标记）
            cleaned_content = re.sub(r'^```(json)?\s*|\s*```$', '', test_cases, flags=re.MULTILINE).strip()

            try:
                test_cases = json.loads(cleaned_content)
            except (ValueError, SyntaxError) as e:
                logger.error(f"解析bug测试用例失败：{str(e)}")
                return []

            # 限制测试用例数量
            if len(test_cases) > max_test_cases:
                test_cases = test_cases[:max_test_cases]

            logger.info(f"最终生成 {len(test_cases)} 个测试用例")
            return test_cases
        except Exception as e:
            logger.error(f"生成bug测试用例时发生错误：{str(e)}")
            return []
    
    def save_bug_recall_to_excel(self, result_data, file_path):
        """
        将bug召回结果保存到Excel文件
        :param result_data: 结果数据
        :param file_path: 文件路径
        """
        try:
            # 从配置获取Excel设置
            excel_config = get_config('excel') or {}
            sheets_config = excel_config.get('sheets', {})
            column_width = excel_config.get('column_width', 20)
            text_wrap = excel_config.get('text_wrap', True)

            # 创建Excel writer对象
            writer = pd.ExcelWriter(file_path, engine='xlsxwriter')

            # 需求信息工作表
            story_info = result_data['story_info']
            story_df = pd.DataFrame([story_info])
            sheet_name = sheets_config.get('story_info', '需求信息')
            story_df.to_excel(writer, sheet_name=sheet_name, index=False)

            # 相关Bug工作表
            relevant_bugs = result_data['relevant_bugs']
            if relevant_bugs:
                # 处理Bug数据，添加TRAG评分信息
                processed_bugs = []
                for bug in relevant_bugs:
                    processed_bug = bug.copy()
                    # 确保包含TRAG相关性评分
                    if 'trag_relevance_score' in bug:
                        processed_bug['TRAG相似度评分'] = bug['trag_relevance_score']
                    if 'relevance_score' in bug:
                        processed_bug['LLM相关性评分'] = bug['relevance_score']
                    processed_bugs.append(processed_bug)

                bugs_df = pd.DataFrame(processed_bugs)
                sheet_name = sheets_config.get('relevant_bugs', '相关Bug')
                bugs_df.to_excel(writer, sheet_name=sheet_name, index=False)

            # 建议测试用例工作表
            test_cases = result_data['suggested_test_cases']
            if test_cases:
                # 处理测试步骤（将数组转换为字符串）
                processed_cases = []
                for case in test_cases:
                    processed_case = case.copy()
                    if 'test_steps' in processed_case and isinstance(processed_case['test_steps'], list):
                        processed_case['test_steps'] = '\n'.join([f"{i+1}. {step}" for i, step in enumerate(processed_case['test_steps'])])
                    processed_cases.append(processed_case)

                cases_df = pd.DataFrame(processed_cases)
                sheet_name = sheets_config.get('test_cases', '建议测试用例')
                cases_df.to_excel(writer, sheet_name=sheet_name, index=False)

            # 获取工作簿对象
            workbook = writer.book

            # 设置列宽格式
            format_wrap_obj = workbook.add_format({'text_wrap': text_wrap})

            # 为每个工作表设置列宽
            for sheet_name in writer.sheets:
                worksheet = writer.sheets[sheet_name]
                worksheet.set_column('A:Z', column_width, format_wrap_obj)

            # 保存Excel文件
            writer.close()
            logger.info(f"Bug召回结果已保存到: {file_path}")

        except Exception as e:
            logger.error(f"保存Bug召回结果失败: {str(e)}")
    
    def recall_bugs_by_story_url(self, story_url):
        """
        根据需求链接通过TRAG向量搜索召回相关BUG
        :param story_url: 需求链接
        :return: 召回结果字典
        """
        try:
            # 获取需求信息
            story = self.tapd_utils.get_story(story_url)
            if not story:
                logger.error("无法获取需求信息，请检查链接是否正确")
                return None

            logger.info(f"开始分析需求: {story['name']}")

            # 从TRAG向量数据库搜索相关Bug
            bugs = self.search_bugs_from_trag(story)
            if not bugs:
                logger.warning("未从Bug知识库中找到相关Bug")
                return {
                    "story_info": {
                        "name": story.get('name', ''),
                        "description": story.get('description', ''),
                        "category": story.get('category_name', ''),
                        "search_method": "TRAG向量搜索"
                    },
                    "relevant_bugs": [],
                    "suggested_test_cases": [],
                    "search_stats": {
                        "total_searched": 0,
                        "trag_filtered": 0,
                        "llm_filtered": 0,
                        "final_count": 0
                    }
                }

            logger.info(f"从TRAG搜索到 {len(bugs)} 个相关Bug")

            # 分析bug相关性（结合TRAG评分和LLM分析）
            relevant_bugs = self.analyze_bugs_relevance(story, bugs)
            logger.info(f"经过相关性分析，筛选出 {len(relevant_bugs)} 个高度相关的Bug")

            # 生成bug测试用例建议
            test_cases = self.generate_bug_test_cases(story, relevant_bugs)
            logger.info(f"基于相关Bug生成 {len(test_cases)} 个建议测试用例")

            # 构建结果数据
            result_data = {
                "story_info": {
                    "name": story.get('name', ''),
                    "description": story.get('description', ''),
                    "category": story.get('category_name', ''),
                    "search_method": "TRAG向量搜索"
                },
                "relevant_bugs": relevant_bugs,
                "suggested_test_cases": test_cases,
                "search_stats": {
                    "total_searched": len(bugs),
                    "trag_filtered": len([b for b in bugs if b.get('trag_relevance_score', 0) > 0]),
                    "llm_filtered": len(relevant_bugs),
                    "final_count": len(test_cases)
                }
            }

            # 保存到Excel文件
            file_path = self.gen_bug_file_path(story['name'])
            self.save_bug_recall_to_excel(result_data, file_path)
            result_data['file_path'] = file_path

            return result_data

        except Exception as e:
            logger.error(f"Bug召回过程中发生错误：{str(e)}")
            return None


if __name__ == "__main__":
    # 测试用例
    print("=== Bug召回工具测试 ===")
    print("基于TRAG向量搜索的Bug召回功能")

    # 初始化工具（需要配置Bug知识库）
    bug_tool = BugRecallTool()

    # 测试需求链接
    story_url = "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472123851503"
    print(f"测试需求链接: {story_url}")

    result = bug_tool.recall_bugs_by_story_url(story_url)
    if result:
        print("\n✅ Bug召回完成")
        print(f"📁 文件路径: {result.get('file_path', '')}")
        print(f"🔍 搜索方法: {result.get('story_info', {}).get('search_method', '')}")

        # 显示搜索统计
        stats = result.get('search_stats', {})
        print(f"📊 搜索统计:")
        print(f"  - TRAG搜索到: {stats.get('total_searched', 0)} 个Bug")
        print(f"  - TRAG筛选后: {stats.get('trag_filtered', 0)} 个Bug")
        print(f"  - LLM筛选后: {stats.get('llm_filtered', 0)} 个Bug")
        print(f"  - 生成测试用例: {stats.get('final_count', 0)} 个")

        print(f"🐛 相关Bug数量: {len(result.get('relevant_bugs', []))}")
        print(f"📝 建议测试用例数量: {len(result.get('suggested_test_cases', []))}")

        # 显示前几个相关Bug
        bugs = result.get('relevant_bugs', [])
        if bugs:
            print(f"\n前3个相关Bug:")
            for i, bug in enumerate(bugs[:3], 1):
                title = bug.get('title', '未知标题')
                trag_score = bug.get('trag_relevance_score', 0)
                llm_score = bug.get('relevance_score', 0)
                print(f"  {i}. {title}")
                print(f"     TRAG评分: {trag_score:.2f}, LLM评分: {llm_score}")
    else:
        print("❌ Bug召回失败")
        print("请检查:")
        print("1. TAPD需求链接是否正确")
        print("2. Bug知识库配置是否正确")
        print("3. 环境变量BUG_NAMESPACE和BUG_COLLECTION是否设置")

